import AppKit
import Carbon

// Adapted from Rectangle (https://github.com/rxhanson/Rectangle) under GPL-3.0
class WindowMover {
    private let accessibilityElement: AccessibilityElement
    private let calculationService: WindowCalculationService
    private let screenDetection: ScreenDetectionService
    private let logger = LoggingService.shared
    private let serviceName = "WindowMover"

    init(
        accessibilityElement: AccessibilityElement = AccessibilityElement(),
        calculationService: WindowCalculationService = WindowCalculationService(),
        screenDetection: ScreenDetectionService = ScreenDetectionService()
    ) {
        self.accessibilityElement = accessibilityElement
        self.calculationService = calculationService
        self.screenDetection = screenDetection

        logger.debug("Initialized", service: serviceName)
    }

    func moveWindow(
        _ window: AXUIElement,
        to direction: WindowDirection,
        on screen: NSScreen,
        frameAdjustment: CGRect? = nil
    ) async throws {
        // Clear any existing debug overlays to prevent performance issues
        ScreenshotDebugger.shared.clearAllDebugOverlays()
        // Get current window info
        guard let windowInfo = try? await accessibilityElement.windowInfo(for: window) else {
            throw WindowMoveError.failedToGetWindowInfo
        }

        // Calculate target frame in Cocoa coordinates
        var targetFrame = calculationService.calculateWindowRect(
            for: direction,
            window: windowInfo,
            screen: screen
        )

        // Log the target frame in detail
        logger.debug("Target frame: \(targetFrame)", service: serviceName)
        logger.debug(
            "Target frame origin: (\(targetFrame.origin.x), \(targetFrame.origin.y))",
            service: serviceName)
        logger.debug(
            "Target frame size: (\(targetFrame.size.width), \(targetFrame.size.height))",
            service: serviceName)

        // Check if this is a bottom-aligned window
        // For bottomHalf, bottomLeft, bottomRight, etc., we need to explicitly check the direction
        let isBottomAligned =
            direction == .bottomHalf || direction == .bottomLeft || direction == .bottomRight
            || direction == .bottomLeftQuarter || direction == .bottomRightQuarter
        logger.debug(
            "Is bottom aligned (based on direction): \(isBottomAligned)", service: serviceName)

        // Log the screen information
        logger.debug("Screen frame: \(screen.frame)", service: serviceName)
        logger.debug("Screen visible frame: \(screen.visibleFrame)", service: serviceName)
        logger.debug(
            "Screen backing scale factor: \(screen.backingScaleFactor)", service: serviceName)

        // Draw a debug overlay to visualize the target rect
        ScreenshotDebugger.shared.drawDebugOverlay(rect: targetFrame)

        // Apply any frame adjustments
        if let adjustment = frameAdjustment {
            targetFrame = targetFrame.offsetBy(
                dx: adjustment.origin.x,
                dy: adjustment.origin.y
            )
            targetFrame.size.width += adjustment.size.width
            targetFrame.size.height += adjustment.size.height
        }

        // Ensure the frame is properly adjusted for the screen
        // Apply the boundary adjustment to make sure the window fits within the screen's visible frame
        let boundaryAdjustedFrame = DisplayScaleManager.shared.adjustRectForScreen(
            rect: targetFrame, screen: screen)
        targetFrame = boundaryAdjustedFrame
        logger.debug("After boundary adjustment: \(targetFrame)", service: serviceName)

        // Move window with efficient retry mechanism
        logger.debug("Moving window with isBottomAligned: \(isBottomAligned)", service: serviceName)
        try await moveWindowWithRetry(
            window, to: targetFrame, isBottomAligned: isBottomAligned, direction: direction,
            targetScreen: screen)
    }

    private func moveWindowWithRetry(
        _ window: AXUIElement,
        to frame: CGRect,
        retryCount: Int = 3,
        isBottomAligned: Bool = false,
        direction: WindowDirection? = nil,
        targetScreen: NSScreen? = nil
    ) async throws {
        var lastError: Error?

        // Check if we can get window info (validates the window is accessible)
        guard (try? await accessibilityElement.windowInfo(for: window)) != nil else {
            throw WindowMoveError.failedToGetWindowInfo
        }

        // Use the target screen passed from moveWindow, not the screen detected from the frame
        // This prevents issues where coordinate flipping causes wrong screen detection
        let screen = targetScreen ?? screenDetection.getScreenContaining(frame) ?? NSScreen.main!

        // Use a unified approach for all displays
        for attempt in 1...retryCount {
            do {
                // Get the current screen's visible frame before setting the frame
                let visibleFrameBefore = screen.visibleFrame

                // Flip the coordinates using Rectangle's approach
                // This ensures a 1-to-1 match with Rectangle's window positioning
                let flippedFrame = frame.screenFlipped
                logger.debug(
                    "Original frame: \(frame), Flipped frame: \(flippedFrame)",
                    service: serviceName)
                try await accessibilityElement.setFrame(
                    window, flippedFrame, isBottomAligned: isBottomAligned)

                // Verify the window moved to the correct position
                let currentFrame = try await accessibilityElement.getFrame(window)

                // Check if the dock has appeared or disappeared during this operation
                let visibleFrameAfter = screen.visibleFrame
                let dockHeightChanged =
                    abs(visibleFrameBefore.height - visibleFrameAfter.height) > 10
                let dockPositionChanged = abs(visibleFrameBefore.minY - visibleFrameAfter.minY) > 10

                // Also check if the window is not at the correct Y position
                let windowNotAtCorrectY =
                    isBottomAligned && abs(currentFrame.minY - visibleFrameAfter.minY) > 10

                if dockHeightChanged || dockPositionChanged || windowNotAtCorrectY {
                    logger.debug(
                        "Detected dock appearance/disappearance during window positioning",
                        service: serviceName)
                    logger.debug(
                        "Visible frame before: \(visibleFrameBefore), after: \(visibleFrameAfter)",
                        service: serviceName)

                    // If the dock appeared/disappeared and this is a bottom-aligned window,
                    // we need to recalculate the target frame and try again
                    if isBottomAligned {
                        logger.debug(
                            "Recalculating frame for bottom-aligned window after dock change",
                            service: serviceName)

                        // Get the window info again
                        if let windowInfo = try? await accessibilityElement.windowInfo(for: window)
                        {
                            // Recalculate the target frame with the new visible frame
                            // Use the provided direction if available, otherwise fall back to bottomHalf
                            let recalculationDirection = direction ?? .bottomHalf

                            logger.debug(
                                "Recalculating with direction: \(recalculationDirection)",
                                service: serviceName,
                                category: .windowPositioning
                            )

                            let newTargetFrame = calculationService.calculateWindowRect(
                                for: recalculationDirection,
                                window: windowInfo,
                                screen: screen
                            )

                            logger.debug(
                                "Original target frame: \(frame), new target frame: \(newTargetFrame)",
                                service: serviceName)

                            // Try to position with the new frame
                            // Flip the coordinates using Rectangle's approach
                            // This ensures a 1-to-1 match with Rectangle's window positioning
                            let flippedFrame = newTargetFrame.screenFlipped
                            logger.debug(
                                "Original frame: \(newTargetFrame), Flipped frame: \(flippedFrame)",
                                service: serviceName)
                            try await accessibilityElement.setFrame(
                                window, flippedFrame, isBottomAligned: true)

                            // Get the final position
                            let finalFrame = try await accessibilityElement.getFrame(window)

                            // Check if the window is still not at the correct Y position
                            // This happens when the window is partially overlapping with the dock
                            if abs(finalFrame.minY - visibleFrameAfter.minY) > 10 {
                                logger.debug(
                                    "Window still not at correct Y position. Current Y: \(finalFrame.minY), Should be: \(visibleFrameAfter.minY)",
                                    service: serviceName)

                                // Create a corrected frame with the proper Y position
                                let correctedFrame = CGRect(
                                    x: finalFrame.minX,
                                    y: visibleFrameAfter.minY,  // Use the visible frame's minY
                                    width: finalFrame.width,
                                    height: finalFrame.height
                                )

                                logger.debug(
                                    "Applying final Y position correction: \(correctedFrame)",
                                    service: serviceName)

                                // Apply the correction
                                // Flip the coordinates using Rectangle's approach
                                // This ensures a 1-to-1 match with Rectangle's window positioning
                                let flippedFrame = correctedFrame.screenFlipped
                                logger.debug(
                                    "Original frame: \(correctedFrame), Flipped frame: \(flippedFrame)",
                                    service: serviceName)
                                try await accessibilityElement.setFrame(
                                    window, flippedFrame, isBottomAligned: true)

                                // Get the final position after correction
                                let correctedFinalFrame = try await accessibilityElement.getFrame(
                                    window)
                                logger.debug(
                                    "Final frame after Y position correction: \(correctedFinalFrame)",
                                    service: serviceName)

                                // Draw a debug overlay
                                ScreenshotDebugger.shared.drawDebugOverlay(
                                    rect: correctedFinalFrame, color: .blue)
                            } else {
                                logger.debug(
                                    "Final frame after dock change adjustment: \(finalFrame)",
                                    service: serviceName)

                                // Draw a debug overlay
                                ScreenshotDebugger.shared.drawDebugOverlay(
                                    rect: finalFrame, color: .blue)
                            }
                            return
                        }
                    }
                }

                // Use a larger tolerance for later attempts and secondary displays
                let isSecondary = screen != NSScreen.main
                let tolerance = (attempt > 1 ? 2.0 : 1.0) * (isSecondary ? 2.0 : 1.0)

                if framesAreEqual(
                    currentFrame, frame, tolerance: tolerance, isBottomAligned: isBottomAligned)
                {
                    logger.debug(
                        "Window positioned successfully on attempt \(attempt)", service: serviceName
                    )

                    // Draw a debug overlay to visualize the actual rect (in a different color)
                    let color: NSColor = isSecondary ? .blue : .green
                    ScreenshotDebugger.shared.drawDebugOverlay(rect: currentFrame, color: color)
                    return
                }

                logger.debug(
                    "Window position verification failed on attempt \(attempt), retrying...",
                    service: serviceName)

                // Increase delay with each attempt
                try await Task.sleep(nanoseconds: UInt64(50_000_000 * attempt))  // Increasing delay with each attempt
            } catch {
                lastError = error
                logger.error("Error setting window frame: \(error)", service: serviceName)
                if attempt == retryCount { throw error }
            }
        }

        throw lastError ?? WindowMoveError.verificationFailed
    }

    private func framesAreEqual(
        _ frame1: CGRect, _ frame2: CGRect, tolerance: CGFloat, isBottomAligned: Bool = false
    ) -> Bool {
        // For bottom-aligned windows, we need to be more lenient with Y position
        // but not too lenient to mask real positioning issues
        let bottomTolerance = isBottomAligned ? 150.0 : tolerance

        // Log the comparison for debugging
        logger.debug("Comparing frames with tolerance \(tolerance):", service: serviceName)
        logger.debug("  Frame1: \(frame1)", service: serviceName)
        logger.debug("  Frame2: \(frame2)", service: serviceName)
        logger.debug(
            "  Is bottom aligned (from parameter): \(isBottomAligned), Y tolerance: \(bottomTolerance)",
            service: serviceName)

        // Calculate differences
        let xDiff = abs(frame1.origin.x - frame2.origin.x)
        let yDiff = abs(frame1.origin.y - frame2.origin.y)
        let widthDiff = abs(frame1.size.width - frame2.size.width)
        let heightDiff = abs(frame1.size.height - frame2.size.height)

        logger.debug(
            "  Differences - X: \(xDiff), Y: \(yDiff), Width: \(widthDiff), Height: \(heightDiff)",
            service: serviceName)

        // Special handling for bottom-aligned windows when the dock might have appeared
        let positionOK: Bool
        if isBottomAligned {
            // Check if this might be a dock appearance/disappearance scenario
            // In this case, we care more about the window being at the bottom of the screen
            // than the exact Y coordinate
            let screen = NSScreen.screens.first { $0.frame.contains(frame1.center) }
            if let screen = screen {
                // Get the screen's visible frame (accounts for dock)
                let visibleFrame = screen.visibleFrame

                // Calculate how far the window is from the bottom of the visible frame
                let frame1BottomToScreenBottom = abs(frame1.minY - visibleFrame.minY)
                let frame2BottomToScreenBottom = abs(frame2.minY - visibleFrame.minY)

                logger.debug(
                    "  Bottom alignment check - Frame1 distance from bottom: \(frame1BottomToScreenBottom), Frame2 distance from bottom: \(frame2BottomToScreenBottom)",
                    service: serviceName)

                // For bottom-aligned windows, we want to be stricter about the Y position
                // The window should be exactly at the bottom of the visible frame (accounting for tolerance)
                let bottomPositionOK = frame1BottomToScreenBottom <= tolerance

                // X position should still be within tolerance
                positionOK =
                    xDiff <= tolerance && (yDiff <= bottomTolerance || bottomPositionOK)
            } else {
                // Fallback to standard check with increased tolerance
                positionOK = xDiff <= tolerance && yDiff <= bottomTolerance
            }
        } else {
            // Standard position check for non-bottom-aligned windows
            positionOK = xDiff <= tolerance && yDiff <= tolerance
        }

        // For size, we're more lenient - some windows have minimum size constraints
        // We'll consider it OK if the window is at least as big as we wanted
        let sizeOK =
            (frame1.size.width >= frame2.size.width * 0.9 || widthDiff <= tolerance * 5)
            && (frame1.size.height >= frame2.size.height * 0.9 || heightDiff <= tolerance * 5)

        // The result is OK if both position and size are acceptable
        let result = positionOK && sizeOK

        logger.debug("  Position OK: \(positionOK), Size OK: \(sizeOK)", service: serviceName)
        logger.debug("  Result: \(result)", service: serviceName)
        return result
    }

    /// Helper to determine if screens are arranged vertically
    private func isVerticalScreenArrangement(screens: [NSScreen]) -> Bool {
        guard screens.count > 1 else { return false }

        // Calculate the total width and height of the arrangement
        var minX: CGFloat = .infinity
        var maxX: CGFloat = -.infinity
        var minY: CGFloat = .infinity
        var maxY: CGFloat = -.infinity

        for screen in screens {
            minX = min(minX, screen.frame.minX)
            maxX = max(maxX, screen.frame.maxX)
            minY = min(minY, screen.frame.minY)
            maxY = max(maxY, screen.frame.maxY)
        }

        let totalWidth = maxX - minX
        let totalHeight = maxY - minY

        // Check if any screen is positioned below another screen
        var hasVerticalStacking = false

        // For macOS, we need to be more careful about detecting vertical stacking
        // because the coordinate system has (0,0) at the bottom-left
        if screens.count == 2 {
            let screen1 = screens[0]
            let screen2 = screens[1]

            // Calculate the vertical overlap percentage
            let verticalOverlap =
                min(screen1.frame.maxY, screen2.frame.maxY)
                - max(screen1.frame.minY, screen2.frame.minY)
            let minHeight = min(screen1.frame.height, screen2.frame.height)
            let verticalOverlapPercentage = verticalOverlap / minHeight

            // Calculate the horizontal overlap percentage
            let horizontalOverlap =
                min(screen1.frame.maxX, screen2.frame.maxX)
                - max(screen1.frame.minX, screen2.frame.minX)
            let minWidth = min(screen1.frame.width, screen2.frame.width)
            let horizontalOverlapPercentage = horizontalOverlap / minWidth

            // If there's significant horizontal overlap and minimal vertical overlap,
            // it's likely a vertical arrangement
            hasVerticalStacking =
                horizontalOverlapPercentage > 0.5 && verticalOverlapPercentage < 0.2

            // Also check if one screen is completely above the other
            if !hasVerticalStacking {
                hasVerticalStacking =
                    (screen1.frame.minY >= screen2.frame.maxY)
                    || (screen2.frame.minY >= screen1.frame.maxY)
            }
        } else {
            // For more than 2 screens, use the original approach
            for i in 0..<screens.count {
                for j in (i + 1)..<screens.count {
                    let screen1 = screens[i]
                    let screen2 = screens[j]

                    // Check if one screen is below the other
                    if (screen1.frame.minY >= screen2.frame.maxY)
                        || (screen2.frame.minY >= screen1.frame.maxY)
                    {
                        hasVerticalStacking = true
                        break
                    }
                }
                if hasVerticalStacking { break }
            }
        }

        // Consider it vertical if either:
        // 1. The total height is significantly larger than width
        // 2. We detected screens stacked on top of each other
        let isVertical = (totalHeight > totalWidth * 1.2) || hasVerticalStacking

        logger.debug("isVerticalScreenArrangement result: \(isVertical)", service: serviceName)
        logger.debug(
            "  Total width: \(totalWidth), total height: \(totalHeight)", service: serviceName)
        logger.debug("  Has vertical stacking: \(hasVerticalStacking)", service: serviceName)

        return isVertical
    }
}
