import AppKit
import CoreGraphics
import Foundation

class WindowCalculationService {
    private let screenDetection: ScreenDetectionService
    private let logger = LoggingService.shared
    private let serviceName = "WindowCalculationService"

    // Calculation classes
    private let standardPositionCalculation = StandardPositionCalculation()
    private let almostMaximizeCalculation = AlmostMaximizeCalculation()
    private let changeSizeCalculation = ChangeSizeCalculation()
    private let specifiedCalculation = SpecifiedCalculation()
    private let leftRightHalfCalculation = LeftRightHalfCalculation()
    private let topBottomHalfCalculation = TopBottomHalfCalculation()

    init(screenDetection: ScreenDetectionService = ScreenDetectionService()) {
        self.screenDetection = screenDetection
        logger.info("Initialized with calculation classes", service: serviceName)
    }

    func calculateWindowRect(
        for direction: WindowDirection,
        window: WindowInfo,
        screen: NSScreen,
        visibleFrameOnly: Bool = true
    ) -> CGRect {
        // Log screen information for debugging
        ScreenDebugger.shared.logScreenInfo()
        // Create calculation parameters
        let screenFrame = visibleFrameOnly ? screen.visibleFrame : screen.frame

        // Get all available screens for multi-display calculations
        let allScreens = screenDetection.getAllScreens()

        // Create window calculation parameters
        let windowParams = WindowCalculationParameters(
            window: window,
            screens: allScreens,
            currentScreen: screen,
            action: direction
        )

        // Create rect calculation parameters for simple calculations
        let rectParams = RectCalculationParameters(
            window: window,
            visibleFrameOfScreen: screenFrame,
            action: direction,
            frameOfScreen: screen.frame
        )

        // Log the calculation request
        logger.debug("Calculating window rect for direction: \(direction)", service: serviceName)

        // Select the appropriate calculation class based on the direction and screen
        var result: CGRect

        // Check if this is a secondary display
        let isSecondary = screen != NSScreen.main

        // Log detailed screen information for debugging
        logger.debug("Is secondary display: \(isSecondary)", service: serviceName)
        logger.debug("Screen frame: \(screen.frame)", service: serviceName)
        logger.debug("Screen visible frame: \(screen.visibleFrame)", service: serviceName)
        logger.debug(
            "Screen backing scale factor: \(screen.backingScaleFactor)", service: serviceName)

        if let screenNumber = screen.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")]
            as? NSNumber
        {
            logger.debug("Screen number: \(screenNumber)", service: serviceName)
        }

        if let mainScreenNumber = NSScreen.main?.deviceDescription[
            NSDeviceDescriptionKey("NSScreenNumber")] as? NSNumber
        {
            logger.debug("Main screen number: \(mainScreenNumber)", service: serviceName)
        }

        // Use a unified approach for all displays
        switch direction {
        case .leftHalf, .rightHalf:
            // Use the left/right half calculation with multi-display support
            if let calcResult = leftRightHalfCalculation.calculate(windowParams) {
                result = calcResult.rect
            } else {
                // Fallback to standard calculation
                result = standardPositionCalculation.calculateRect(rectParams).rect
            }

        case .topHalf, .bottomHalf:
            // Use the top/bottom half calculation with multi-display support
            if let calcResult = topBottomHalfCalculation.calculate(windowParams) {
                result = calcResult.rect
            } else {
                // Fallback to standard calculation
                result = standardPositionCalculation.calculateRect(rectParams).rect
            }

        case .almostMaximize:
            result = almostMaximizeCalculation.calculateRect(rectParams).rect

        case .larger, .smaller, .largerWidth, .smallerWidth:
            result = changeSizeCalculation.calculateRect(rectParams).rect

        case .specified:
            result = specifiedCalculation.calculateRect(rectParams).rect

        default:
            // Use standard position calculation for all other directions
            result = standardPositionCalculation.calculateRect(rectParams).rect
        }

        // NOTE: Removed unnecessary coordinate conversion that was causing windows to snap to wrong screens
        // The calculation methods (TopBottomHalfCalculation, etc.) already calculate the rect for the correct target screen
        // Converting from main screen to target screen was causing incorrect coordinate transformations
        logger.debug(
            "Skipping coordinate conversion - rect already calculated for target screen",
            service: serviceName)

        // Ensure the rect fits within the screen's visible frame for all displays
        let boundaryAdjustedRect = DisplayScaleManager.shared.adjustRectForScreen(
            rect: result, screen: screen)
        result = boundaryAdjustedRect
        logger.debug("After boundary adjustment: \(result)", service: serviceName)

        logger.debug("Calculated rect: \(result)", service: serviceName)
        return result
    }

    /// Helper to determine if screens are arranged vertically
    private func isVerticalScreenArrangement(screens: [NSScreen]) -> Bool {
        guard screens.count > 1 else { return false }

        // Calculate the total width and height of the arrangement
        var minX: CGFloat = .infinity
        var maxX: CGFloat = -.infinity
        var minY: CGFloat = .infinity
        var maxY: CGFloat = -.infinity

        for screen in screens {
            minX = min(minX, screen.frame.minX)
            maxX = max(maxX, screen.frame.maxX)
            minY = min(minY, screen.frame.minY)
            maxY = max(maxY, screen.frame.maxY)
        }

        let totalWidth = maxX - minX
        let totalHeight = maxY - minY

        // Check if any screen is positioned below another screen
        var hasVerticalStacking = false

        // For macOS, we need to be more careful about detecting vertical stacking
        // because the coordinate system has (0,0) at the bottom-left
        if screens.count == 2 {
            let screen1 = screens[0]
            let screen2 = screens[1]

            // Calculate the vertical overlap percentage
            let verticalOverlap =
                min(screen1.frame.maxY, screen2.frame.maxY)
                - max(screen1.frame.minY, screen2.frame.minY)
            let minHeight = min(screen1.frame.height, screen2.frame.height)
            let verticalOverlapPercentage = verticalOverlap / minHeight

            // Calculate the horizontal overlap percentage
            let horizontalOverlap =
                min(screen1.frame.maxX, screen2.frame.maxX)
                - max(screen1.frame.minX, screen2.frame.minX)
            let minWidth = min(screen1.frame.width, screen2.frame.width)
            let horizontalOverlapPercentage = horizontalOverlap / minWidth

            // If there's significant horizontal overlap and minimal vertical overlap,
            // it's likely a vertical arrangement
            hasVerticalStacking =
                horizontalOverlapPercentage > 0.5 && verticalOverlapPercentage < 0.2

            // Also check if one screen is completely above the other
            if !hasVerticalStacking {
                hasVerticalStacking =
                    (screen1.frame.minY >= screen2.frame.maxY)
                    || (screen2.frame.minY >= screen1.frame.maxY)
            }
        } else {
            // For more than 2 screens, use the original approach
            for i in 0..<screens.count {
                for j in (i + 1)..<screens.count {
                    let screen1 = screens[i]
                    let screen2 = screens[j]

                    // Check if one screen is below the other
                    if (screen1.frame.minY >= screen2.frame.maxY)
                        || (screen2.frame.minY >= screen1.frame.maxY)
                    {
                        hasVerticalStacking = true
                        break
                    }
                }
                if hasVerticalStacking { break }
            }
        }

        // Consider it vertical if either:
        // 1. The total height is significantly larger than width
        // 2. We detected screens stacked on top of each other
        let isVertical = (totalHeight > totalWidth * 1.2) || hasVerticalStacking

        logger.debug("isVerticalScreenArrangement result: \(isVertical)", service: serviceName)
        logger.debug(
            "  Total width: \(totalWidth), total height: \(totalHeight)", service: serviceName)
        logger.debug("  Has vertical stacking: \(hasVerticalStacking)", service: serviceName)

        return isVertical
    }
}
