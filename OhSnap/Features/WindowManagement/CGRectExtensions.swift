import AppKit
import CoreGraphics

extension CGRect {
    /// Returns the center point of the rectangle
    var center: CGPoint {
        return CGPoint(x: midX, y: midY)
    }

    /// Flips the Y coordinate for the AX coordinate system
    /// Fixed to use the actual main display (coordinates 0,0) instead of NSScreen.screens[0]
    var screenFlipped: CGRect {
        guard !isNull else {
            return self
        }

        // Find the actual main display (the one at coordinates 0,0)
        // This is crucial for vertical arrangements where main display might not be screens[0]
        let mainDisplay =
            NSScreen.screens.first { screen in
                screen.frame.minX == 0 && screen.frame.minY == 0
            } ?? NSScreen.screens[0]  // Fallback to first screen if no main found

        return .init(
            origin: .init(x: origin.x, y: mainDisplay.frame.maxY - maxY), size: size)
    }

    /// Returns true if the rectangle is wider than it is tall
    var isLandscape: Bool { width > height }

    /// Returns the number of edges shared with another rectangle
    func numSharedEdges(withRect rect: CGRect) -> Int {
        var sharedEdgeCount = 0
        if minX == rect.minX { sharedEdgeCount += 1 }
        if maxX == rect.maxX { sharedEdgeCount += 1 }
        if minY == rect.minY { sharedEdgeCount += 1 }
        if maxY == rect.maxY { sharedEdgeCount += 1 }
        return sharedEdgeCount
    }
}

extension CGPoint {
    /// Flips the Y coordinate for the AX coordinate system
    /// Fixed to use the actual main display (coordinates 0,0) instead of NSScreen.screens[0]
    var screenFlipped: CGPoint {
        // Find the actual main display (the one at coordinates 0,0)
        // This is crucial for vertical arrangements where main display might not be screens[0]
        let mainDisplay =
            NSScreen.screens.first { screen in
                screen.frame.minX == 0 && screen.frame.minY == 0
            } ?? NSScreen.screens[0]  // Fallback to first screen if no main found

        return .init(x: x, y: mainDisplay.frame.maxY - y)
    }
}
