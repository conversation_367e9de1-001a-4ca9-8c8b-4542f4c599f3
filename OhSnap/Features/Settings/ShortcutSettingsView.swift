import AppKit
import Carbon.HIToolbox
import Foundation
import KeyboardShortcuts
import MA<PERSON>hortcut
import SwiftUI

struct ShortcutSettingsView: View {
    @State private var shortcutsVersion = 0
    @EnvironmentObject var workspaceService: WorkspaceService

    // Organize shortcuts by logical groups
    let halves = [
        ("Left Half", "leftHalf"),
        ("Right Half", "rightHalf"),
        ("Top Half", "topHalf"),
        ("Bottom Half", "bottomHalf"),
    ]

    let quarters = [
        ("Top Left Quarter", "topLeftQuarter"),
        ("Top Right Quarter", "topRightQuarter"),
        ("Bottom Left Quarter", "bottomLeftQuarter"),
        ("Bottom Right Quarter", "bottomRightQuarter"),
    ]

    let thirds = [
        ("Left Third", "leftThird"),
        ("Center Third", "centerThird"),
        ("Right Third", "rightThird"),
        ("Left Two Thirds", "leftTwoThirds"),
        ("Center Two Thirds", "centerTwoThirds"),
        ("Right Two Thirds", "rightTwoThirds"),
    ]

    let other = [
        ("Fullscreen", "fullscreen"),
        ("Save Workspace", "saveWorkspace"),
    ]

    // Combine all shortcuts for reset functionality
    var allShortcuts: [(String, String)] {
        return halves + quarters + thirds + other
    }

    // Helper function to get the KeyboardShortcuts.Name for an action
    private func getShortcutNameForAction(_ action: String) -> KeyboardShortcuts.Name {
        switch action {
        case "leftHalf":
            return .leftHalf
        case "rightHalf":
            return .rightHalf
        case "topHalf":
            return .topHalf
        case "bottomHalf":
            return .bottomHalf
        case "topLeftQuarter":
            return .topLeftQuarter
        case "topRightQuarter":
            return .topRightQuarter
        case "bottomLeftQuarter":
            return .bottomLeftQuarter
        case "bottomRightQuarter":
            return .bottomRightQuarter
        case "leftThird":
            return .leftThird
        case "centerThird":
            return .centerThird
        case "rightThird":
            return .rightThird
        case "leftTwoThirds":
            return .leftTwoThirds
        case "centerTwoThirds":
            return .centerTwoThirds
        case "rightTwoThirds":
            return .rightTwoThirds
        case "fullscreen":
            return .fullscreen
        case "saveWorkspace":
            return .saveWorkspace
        default:
            // This should never happen, but we need a fallback
            return .leftHalf
        }
    }

    var body: some View {
        VStack {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    // Halves section
                    Text("Halves")
                        .ohSnapSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: 0) {
                            ForEach(halves, id: \.1) { (name, action) in
                                ShortcutRow(
                                    name: name,
                                    action: action,
                                    onReset: {
                                        // Trigger a refresh
                                        shortcutsVersion += 1

                                        // Notify the ShortcutService to update shortcuts
                                        if let appDelegate = NSApp.delegate as? AppDelegate {
                                            appDelegate.shortcutService.updateShortcuts()
                                        }
                                    }
                                )
                                .environmentObject(workspaceService)
                                .padding(.vertical, 4)

                                if name != halves.last?.0 {
                                    Divider()
                                }
                            }
                        }
                    }

                    // Quarters section
                    Text("Quarters")
                        .ohSnapSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: 0) {
                            ForEach(quarters, id: \.1) { (name, action) in
                                ShortcutRow(
                                    name: name,
                                    action: action,
                                    onReset: {
                                        // Trigger a refresh
                                        shortcutsVersion += 1

                                        // Notify the ShortcutService to update shortcuts
                                        if let appDelegate = NSApp.delegate as? AppDelegate {
                                            appDelegate.shortcutService.updateShortcuts()
                                        }
                                    }
                                )
                                .environmentObject(workspaceService)
                                .padding(.vertical, 4)

                                if name != quarters.last?.0 {
                                    Divider()
                                }
                            }
                        }
                    }

                    // Thirds section
                    Text("Thirds")
                        .ohSnapSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: 0) {
                            ForEach(thirds, id: \.1) { (name, action) in
                                ShortcutRow(
                                    name: name,
                                    action: action,
                                    onReset: {
                                        // Trigger a refresh
                                        shortcutsVersion += 1

                                        // Notify the ShortcutService to update shortcuts
                                        if let appDelegate = NSApp.delegate as? AppDelegate {
                                            appDelegate.shortcutService.updateShortcuts()
                                        }
                                    }
                                )
                                .environmentObject(workspaceService)
                                .padding(.vertical, 4)

                                if name != thirds.last?.0 {
                                    Divider()
                                }
                            }
                        }
                    }

                    // Other section
                    Text("Other")
                        .ohSnapSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: 0) {
                            ForEach(other, id: \.1) { (name, action) in
                                ShortcutRow(
                                    name: name,
                                    action: action,
                                    onReset: {
                                        // Trigger a refresh
                                        shortcutsVersion += 1

                                        // Notify the ShortcutService to update shortcuts
                                        if let appDelegate = NSApp.delegate as? AppDelegate {
                                            appDelegate.shortcutService.updateShortcuts()
                                        }
                                    }
                                )
                                .environmentObject(workspaceService)
                                .padding(.vertical, 4)

                                if name != other.last?.0 {
                                    Divider()
                                }
                            }
                        }
                    }
                }
                .padding()
            }

            SettingsFooter(buttonTitle: "Reset All to Defaults") {
                // Reset all shortcuts individually to ensure they're reset to their defaults
                for (_, action) in allShortcuts {
                    // Get the corresponding KeyboardShortcuts.Name
                    let shortcutName = getShortcutNameForAction(action)

                    // Reset the shortcut using KeyboardShortcutsBridge
                    KeyboardShortcutsBridge.shared.resetShortcut(for: shortcutName)

                    // Log each reset
                    print("🔄 RESET: Reset shortcut for '\(action)' to default")
                }

                // Update shortcuts in the service
                if let appDelegate = NSApp.delegate as? AppDelegate {
                    appDelegate.shortcutService.updateShortcuts()
                }

                // Trigger a refresh of the UI
                shortcutsVersion += 1

                // Force a UI refresh by posting a notification
                NotificationCenter.default.post(
                    name: .refreshStatusMenu,
                    object: nil
                )

                // Log the reset
                print("🔄 RESET ALL: Reset all shortcuts to defaults")
                let logger = LoggingService.shared
                logger.debug(
                    "Reset all shortcuts to defaults",
                    service: "ShortcutSettingsView",
                    category: .shortcuts
                )
            }
        }
        .onAppear {
            // Migrate existing shortcuts when the view appears
            if let appDelegate = NSApp.delegate as? AppDelegate {
                appDelegate.shortcutService.migrateExistingShortcuts()
            }
        }
    }
}
